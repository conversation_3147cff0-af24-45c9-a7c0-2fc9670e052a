{
  "compilerOptions": {
    "outDir": "./cjs",
    "module": "commonjs",
    "moduleResolution": "node",
    "target": "es2017",
    "checkJs": false,
    "alwaysStrict": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "strict": true,
    "strictFunctionTypes": true,
    "types": [ "node" ],
    "esModuleInterop": false // This is a viral option, do not enable https://www.semver-ts.org/#module-interop
  },
  "include": ["./src/**/*"],
  "exclude": ["./src/stripe.esm.node.ts", "./src/stripe.esm.worker.ts"],
}
