name: CI

on:
  workflow_dispatch: {}
  push:
    branches:
      - master
      - beta
      - sdk-release/**
      - feature/**
    tags:
      - v[0-9]+.[0-9]+.[0-9]+*
  pull_request:
    branches:
      - master
      - beta
      - sdk-release/**
      - feature/**

jobs:
  build:
    name: 'Static Checks'
    runs-on: 'ubuntu-24.04'

    steps:
      - uses: extractions/setup-just@v2
      - uses: actions/checkout@v2

      - name: Setup node
        uses: actions/setup-node@v2

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> "$GITHUB_OUTPUT"

      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Verify Linting
        run: just lint-check

      - name: Verify Formatting
        run: just format-check

  test:
    name: Test (${{ matrix.node }})
    needs: [build]
    strategy:
      fail-fast: false
      matrix:
        os:
          - 'ubuntu-24.04'
        node:
          # should include even numbers >= 12
          # see: https://nodejs.org/en/about/previous-releases
          - '22'
          - '20'
          - '18'
          - '16'
          - '14'
          - '12'
    runs-on: ${{ matrix.os }}
    steps:
      - uses: extractions/setup-just@v2
      - uses: actions/checkout@v2

      - name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node }}

      - name: Print Node.js version
        run: node -v

      # used for one of the integration tests
      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: v1.x

      - name: Print Deno version
        run: deno -V

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> "$GITHUB_OUTPUT"

      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          # searching very deep deps can time out, so only cache on the root yarn.lock
          key: ${{ runner.os }}-yarn-${{ hashFiles('yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - uses: stripe/openapi/actions/stripe-mock@master

      - name: Test
        run: just ci-test

  publish:
    name: Publish
    if: >-
      (github.event_name == 'workflow_dispatch' || github.event_name == 'push') &&
      startsWith(github.ref, 'refs/tags/v') &&
      endsWith(github.actor, '-stripe')
    needs: [build, test]
    runs-on: 'ubuntu-24.04'
    steps:
      # just is called in `yarn prepack`, which is called during the `publish` operation
      - uses: extractions/setup-just@v2
      - uses: actions/checkout@v2
      - run: sudo apt-get install -y oathtool
      - name: Publish to NPM
        run: |
          set +ex
          npm config set //registry.npmjs.org/:_authToken $NPM_AUTH_TOKEN
          # print the NPM user name for validation
          npm whoami
          VERSION=$(node -p "require('./package.json').version" )
          # Only publish stable versions to the latest tag
          if [[ "$VERSION" =~ ^[^-]+$ ]]; then
            NPM_TAG="latest"
          else
            NPM_TAG="beta"
          fi
          echo "Publishing $VERSION with $NPM_TAG tag."
          npm publish --otp="$(oathtool -b --totp $NPM_OTP)" --tag $NPM_TAG
        env:
          NPM_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
          NPM_OTP: ${{ secrets.NPM_OTP }}
      - uses: stripe/openapi/actions/notify-release@master
        if: always()
        with:
          bot_token: ${{ secrets.SLACK_BOT_TOKEN }}
