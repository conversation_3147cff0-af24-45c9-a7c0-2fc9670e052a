// File generated from our OpenAPI spec

import {StripeResource} from '../../StripeResource.js';
const stripeMethod = StripeResource.method;
export const Requests = StripeResource.extend({
  create: stripeMethod({method: 'POST', fullPath: '/v1/forwarding/requests'}),
  retrieve: stripeMethod({
    method: 'GET',
    fullPath: '/v1/forwarding/requests/{id}',
  }),
  list: stripeMethod({
    method: 'GET',
    fullPath: '/v1/forwarding/requests',
    methodType: 'list',
  }),
});
