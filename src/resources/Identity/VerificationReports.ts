// File generated from our OpenAPI spec

import {StripeResource} from '../../StripeResource.js';
const stripeMethod = StripeResource.method;
export const VerificationReports = StripeResource.extend({
  retrieve: stripeMethod({
    method: 'GET',
    fullPath: '/v1/identity/verification_reports/{report}',
  }),
  list: stripeMethod({
    method: 'GET',
    fullPath: '/v1/identity/verification_reports',
    methodType: 'list',
  }),
});
