// File generated from our OpenAPI spec

import {StripeResource} from '../../../StripeResource.js';
const stripeMethod = StripeResource.method;
export const Transactions = StripeResource.extend({
  createForceCapture: stripeMethod({
    method: 'POST',
    fullPath: '/v1/test_helpers/issuing/transactions/create_force_capture',
  }),
  createUnlinkedRefund: stripeMethod({
    method: 'POST',
    fullPath: '/v1/test_helpers/issuing/transactions/create_unlinked_refund',
  }),
  refund: stripeMethod({
    method: 'POST',
    fullPath: '/v1/test_helpers/issuing/transactions/{transaction}/refund',
  }),
});
