// File generated from our OpenAPI spec

import {StripeResource} from '../../StripeResource.js';
const stripeMethod = StripeResource.method;
export const ReportTypes = StripeResource.extend({
  retrieve: stripeMethod({
    method: 'GET',
    fullPath: '/v1/reporting/report_types/{report_type}',
  }),
  list: stripeMethod({
    method: 'GET',
    fullPath: '/v1/reporting/report_types',
    methodType: 'list',
  }),
});
