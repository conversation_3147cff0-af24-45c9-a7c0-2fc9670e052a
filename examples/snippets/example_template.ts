/**
 * example_template.py - This is a template for defining new examples.  It is not intended to be used directly.

 * <describe what this example does>

 * In this example, we:
 *   - <key step 1>
 *   - <key step 2
 *   - ...

 * <describe assumptions about the user's stripe account, environment, or configuration;
 * or things to watch out for when running>
 */

import {Stripe} from 'stripe';

const apiKey = '{{API_KEY}}';

console.log('Hello World');
// const client = new Stripe(apiKey);
// client.v2....
