{
  "compilerOptions": {
    /* Specify ECMAScript target version: 'ES3' (default), 'ES5', 'ES2015', 'ES2016', 'ES2017', 'ES2018', 'ES2019' or 'ESNEXT'. */
    "target": "es5",
    /* Specify module code generation: 'none', 'commonjs', 'amd', 'system', 'umd', 'es2015', or 'ESNext'. */
    "module": "commonjs",

    /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */
    "esModuleInterop": true,

    /* Advanced Options */
    /* Disallow inconsistently-cased references to the same file. */
    "forceConsistentCasingInFileNames": true,

    "experimentalDecorators": true,
  }
}
