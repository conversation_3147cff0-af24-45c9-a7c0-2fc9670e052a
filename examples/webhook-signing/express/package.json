{"name": "webhook-signing-example-express", "version": "1.0.0", "description": "Express webhook parsing sample", "repository": {}, "main": "./main.ts", "scripts": {"run": "ts-node-transpile-only ./main.ts", "prepare": "../prepare.sh"}, "author": "", "license": "ISC", "dependencies": {"dotenv": "^8.2.0", "express": "^4.17.1", "koa": "^2.14.1", "stripe": "^11.9.1"}, "devDependencies": {"eslint": "^8.33.0", "@types/koa": "^2.13.5", "@types/koa-bodyparser": "^4.3.10", "@types/node": "^13.1.4", "typescript": "^4.8.3", "ts-node": "^10.9.1"}}