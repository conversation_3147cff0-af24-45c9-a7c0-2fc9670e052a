{"name": "webhook-signing-example-nestjs", "version": "1.0.0", "description": "Nestjs webhook parsing sample", "repository": {}, "main": "./main.ts", "scripts": {"run": "ts-node-transpile-only ./main.ts", "prepare": "../prepare.sh"}, "author": "<PERSON>", "license": "ISC", "dependencies": {"@nestjs/common": "^11.0.16", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.2.1", "dotenv": "^16.3.1", "@nestjs/platform-express": "^10.2.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "stripe": "^12.18.0"}, "devDependencies": {"eslint": "^8.33.0", "@types/node": "^20.5.4", "@types/express": "^4.17.17", "typescript": "^5.2.2", "ts-node": "^10.9.1"}}