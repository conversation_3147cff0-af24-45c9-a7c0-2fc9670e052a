{"name": "webhook-signing-example-nextjs", "version": "1.0.0", "description": "NextJS webhook parsing sample", "repository": {}, "main": "./main.ts", "scripts": {"run": "ts-node-transpile-only ./main.ts", "prepare": "../prepare.sh", "dev": "next dev --port 0"}, "author": "", "license": "ISC", "dependencies": {"dotenv": "^8.2.0", "next": "^13.1.6", "react": "^18.2.0", "react-dom": "^18.2.0", "stripe": "^11.9.1"}, "devDependencies": {"@types/node": "^13.1.4", "@types/react": "18.0.27", "eslint": "^8.33.0", "typescript": "^4.8.3", "ts-node": "^10.9.1"}}